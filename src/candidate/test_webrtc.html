<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Enhanced WebRTC Test Client</title>
  <style>
    :root {
      --primary-color: #4A90E2;
      --success-color: #4F8A10;
      --warning-color: #9F6000;
      --error-color: #D8000C;
      --bg-success: #DFF2BF;
      --bg-warning: #FEEFB3;
      --bg-error: #FFBABA;
      --border-radius: 8px;
      --spacing: 20px;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: var(--spacing);
      background: #f5f5f5;
      line-height: 1.6;
    }

    .header {
      text-align: center;
      margin-bottom: var(--spacing);
      padding: var(--spacing);
      background: white;
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .main-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing);
      margin-bottom: var(--spacing);
    }

    .video-section {
      background: white;
      padding: var(--spacing);
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .video-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .video-wrapper {
      position: relative;
    }

    video {
      width: 100%;
      height: 250px;
      background: #000;
      border-radius: var(--border-radius);
      object-fit: cover;
    }

    .video-overlay {
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
    }

    .controls-section {
      background: white;
      padding: var(--spacing);
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .control-group {
      margin-bottom: 15px;
    }

    .control-group h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    button {
      padding: 10px 16px;
      cursor: pointer;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      transition: all 0.2s;
      min-width: 100px;
    }

    button:hover:not(:disabled) {
      background: #357ABD;
      transform: translateY(-1px);
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    button.success {
      background: #28a745;
    }

    button.warning {
      background: #ffc107;
      color: #333;
    }

    button.danger {
      background: #dc3545;
    }

    .status {
      margin-bottom: 15px;
      padding: 12px;
      border-radius: var(--border-radius);
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .status.connected {
      background-color: var(--bg-success);
      color: var(--success-color);
    }

    .status.connecting {
      background-color: #CCE5FF;
      color: #0066CC;
    }

    .status.disconnected {
      background-color: var(--bg-warning);
      color: var(--warning-color);
    }

    .status.error {
      background-color: var(--bg-error);
      color: var(--error-color);
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: currentColor;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing);
      margin-bottom: var(--spacing);
    }

    .info-panel {
      background: white;
      padding: var(--spacing);
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .info-panel h3 {
      margin: 0 0 15px 0;
      color: #333;
      border-bottom: 2px solid #eee;
      padding-bottom: 5px;
    }

    .metric {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      padding: 5px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .metric:last-child {
      border-bottom: none;
    }

    .metric-label {
      font-weight: 500;
      color: #666;
    }

    .metric-value {
      color: #333;
      font-family: monospace;
    }

    .logs-section {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .logs-header {
      background: #333;
      color: white;
      padding: 15px var(--spacing);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logs-container {
      height: 300px;
      overflow-y: auto;
      background: #1e1e1e;
      color: #f0f0f0;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 13px;
    }

    .log-entry {
      padding: 8px var(--spacing);
      border-bottom: 1px solid #333;
      display: flex;
      align-items: flex-start;
      gap: 10px;
    }

    .log-entry:hover {
      background: #2a2a2a;
    }

    .log-timestamp {
      color: #888;
      min-width: 80px;
      font-size: 11px;
    }

    .log-level {
      min-width: 50px;
      font-weight: bold;
      text-transform: uppercase;
      font-size: 11px;
    }

    .log-level.info { color: #4A90E2; }
    .log-level.success { color: #28a745; }
    .log-level.warning { color: #ffc107; }
    .log-level.error { color: #dc3545; }
    .log-level.debug { color: #6c757d; }

    .log-message {
      flex: 1;
      word-break: break-word;
    }

    .settings-panel {
      background: white;
      padding: var(--spacing);
      border-radius: var(--border-radius);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: var(--spacing);
    }

    .setting-group {
      margin-bottom: 15px;
    }

    .setting-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
    }

    .setting-group input,
    .setting-group select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .setting-group input:focus,
    .setting-group select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    @media (max-width: 768px) {
      .main-container {
        grid-template-columns: 1fr;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .button-group {
        flex-direction: column;
      }

      button {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Enhanced WebRTC Test Client</h1>
    <p>Comprehensive WebRTC testing with advanced debugging and monitoring</p>
  </div>

  <div class="settings-panel">
    <h3>Configuration</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
      <div class="setting-group">
        <label for="sessionId">Session ID:</label>
        <input type="text" id="sessionId" value="test-session" />
      </div>
      <div class="setting-group">
        <label for="wsUrl">WebSocket URL:</label>
        <input type="text" id="wsUrl" value="ws://localhost:8080/ws/interview/" />
      </div>
      <div class="setting-group">
        <label for="videoQuality">Video Quality:</label>
        <select id="videoQuality">
          <option value="low">Low (320x240)</option>
          <option value="medium" selected>Medium (640x480)</option>
          <option value="high">High (1280x720)</option>
        </select>
      </div>
      <div class="setting-group">
        <label for="audioEnabled">Audio:</label>
        <select id="audioEnabled">
          <option value="true" selected>Enabled</option>
          <option value="false">Disabled</option>
        </select>
      </div>
    </div>
  </div>

  <div id="status" class="status disconnected">
    <div class="status-indicator"></div>
    <span>Status: Not initialized</span>
  </div>

  <div class="main-container">
    <div class="video-section">
      <h3>Video Streams</h3>
      <div class="video-container">
        <div class="video-wrapper">
          <video id="localVideo" autoplay muted playsinline></video>
          <div class="video-overlay" id="localVideoInfo">Local Video</div>
        </div>
        <div class="video-wrapper">
          <video id="remoteVideo" autoplay playsinline></video>
          <div class="video-overlay" id="remoteVideoInfo">Remote Video (Loopback)</div>
        </div>
      </div>
    </div>

    <div class="controls-section">
      <div class="control-group">
        <h3>Connection Controls</h3>
        <div class="button-group">
          <button id="startButton">Start Test</button>
          <button id="stopButton" disabled class="danger">Stop Test</button>
          <button id="restartButton" disabled class="warning">Restart</button>
        </div>
      </div>

      <div class="control-group">
        <h3>Media Controls</h3>
        <div class="button-group">
          <button id="toggleVideoButton" disabled>Toggle Video</button>
          <button id="toggleAudioButton" disabled>Toggle Audio</button>
          <button id="switchCameraButton" disabled>Switch Camera</button>
        </div>
      </div>

      <div class="control-group">
        <h3>Test Actions</h3>
        <div class="button-group">
          <button id="pingButton" disabled class="success">Send Ping</button>
          <button id="statsButton" disabled>Get Stats</button>
          <button id="clearLogsButton">Clear Logs</button>
        </div>
      </div>
    </div>
  </div>

  <div class="info-grid">
    <div class="info-panel">
      <h3>Connection Info</h3>
      <div id="connectionInfo">
        <div class="metric">
          <span class="metric-label">State:</span>
          <span class="metric-value" id="connectionState">Disconnected</span>
        </div>
        <div class="metric">
          <span class="metric-label">ICE State:</span>
          <span class="metric-value" id="iceState">New</span>
        </div>
        <div class="metric">
          <span class="metric-label">Signaling State:</span>
          <span class="metric-value" id="signalingState">Stable</span>
        </div>
        <div class="metric">
          <span class="metric-label">Session ID:</span>
          <span class="metric-value" id="currentSessionId">-</span>
        </div>
      </div>
    </div>

    <div class="info-panel">
      <h3>Media Info</h3>
      <div id="mediaInfo">
        <div class="metric">
          <span class="metric-label">Local Tracks:</span>
          <span class="metric-value" id="localTracks">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Remote Tracks:</span>
          <span class="metric-value" id="remoteTracks">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Video Resolution:</span>
          <span class="metric-value" id="videoResolution">-</span>
        </div>
        <div class="metric">
          <span class="metric-label">Frame Rate:</span>
          <span class="metric-value" id="frameRate">-</span>
        </div>
      </div>
    </div>

    <div class="info-panel">
      <h3>Network Stats</h3>
      <div id="networkStats">
        <div class="metric">
          <span class="metric-label">Bytes Sent:</span>
          <span class="metric-value" id="bytesSent">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Bytes Received:</span>
          <span class="metric-value" id="bytesReceived">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Packets Lost:</span>
          <span class="metric-value" id="packetsLost">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Round Trip Time:</span>
          <span class="metric-value" id="roundTripTime">-</span>
        </div>
      </div>
    </div>

    <div class="info-panel">
      <h3>Performance</h3>
      <div id="performanceInfo">
        <div class="metric">
          <span class="metric-label">Connection Time:</span>
          <span class="metric-value" id="connectionTime">-</span>
        </div>
        <div class="metric">
          <span class="metric-label">Reconnect Count:</span>
          <span class="metric-value" id="reconnectCount">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Error Count:</span>
          <span class="metric-value" id="errorCount">0</span>
        </div>
        <div class="metric">
          <span class="metric-label">Uptime:</span>
          <span class="metric-value" id="uptime">-</span>
        </div>
      </div>
    </div>
  </div>

  <div class="logs-section">
    <div class="logs-header">
      <h3>Debug Logs</h3>
      <div>
        <button id="exportLogsButton" style="background: #28a745; padding: 5px 10px; font-size: 12px;">Export Logs</button>
        <button id="clearLogsButton2" style="background: #dc3545; padding: 5px 10px; font-size: 12px; margin-left: 5px;">Clear</button>
      </div>
    </div>
    <div class="logs-container" id="logs"></div>
  </div>

  <script>
    class EnhancedWebRTCClient {
      constructor() {
        // DOM Elements
        this.elements = {
          // Video elements
          localVideo: document.getElementById('localVideo'),
          remoteVideo: document.getElementById('remoteVideo'),
          localVideoInfo: document.getElementById('localVideoInfo'),
          remoteVideoInfo: document.getElementById('remoteVideoInfo'),

          // Control buttons
          startButton: document.getElementById('startButton'),
          stopButton: document.getElementById('stopButton'),
          restartButton: document.getElementById('restartButton'),
          toggleVideoButton: document.getElementById('toggleVideoButton'),
          toggleAudioButton: document.getElementById('toggleAudioButton'),
          switchCameraButton: document.getElementById('switchCameraButton'),
          pingButton: document.getElementById('pingButton'),
          statsButton: document.getElementById('statsButton'),
          clearLogsButton: document.getElementById('clearLogsButton'),
          clearLogsButton2: document.getElementById('clearLogsButton2'),
          exportLogsButton: document.getElementById('exportLogsButton'),

          // Status and info displays
          statusDiv: document.getElementById('status'),
          logsDiv: document.getElementById('logs'),

          // Configuration inputs
          sessionIdInput: document.getElementById('sessionId'),
          wsUrlInput: document.getElementById('wsUrl'),
          videoQualitySelect: document.getElementById('videoQuality'),
          audioEnabledSelect: document.getElementById('audioEnabled'),

          // Info displays
          connectionState: document.getElementById('connectionState'),
          iceState: document.getElementById('iceState'),
          signalingState: document.getElementById('signalingState'),
          currentSessionId: document.getElementById('currentSessionId'),
          localTracks: document.getElementById('localTracks'),
          remoteTracks: document.getElementById('remoteTracks'),
          videoResolution: document.getElementById('videoResolution'),
          frameRate: document.getElementById('frameRate'),
          bytesSent: document.getElementById('bytesSent'),
          bytesReceived: document.getElementById('bytesReceived'),
          packetsLost: document.getElementById('packetsLost'),
          roundTripTime: document.getElementById('roundTripTime'),
          connectionTime: document.getElementById('connectionTime'),
          reconnectCount: document.getElementById('reconnectCount'),
          errorCount: document.getElementById('errorCount'),
          uptime: document.getElementById('uptime')
        };

        // Application state
        this.state = {
          ws: null,
          peerConnection: null,
          localStream: null,
          remoteStream: null,
          candidateQueue: [],
          remoteDescSet: false,
          isActive: false,
          reconnectAttempts: 0,
          startTime: null,
          connectionStartTime: null,
          errorCount: 0,
          videoEnabled: true,
          audioEnabled: true,
          currentCamera: 'user'
        };

        // Configuration
        this.config = {
          sessionId: 'test-session',
          wsUrl: 'ws://localhost:8080/ws/interview/',
          maxReconnectAttempts: 5,
          reconnectDelay: 2000,
          statsInterval: 1000,
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' }
          ]
        };

        // Logging system
        this.logs = [];
        this.maxLogs = 1000;

        // Intervals and timers
        this.intervals = new Set();
        this.timeouts = new Set();

        this.initialize();
      }

      // Initialize the client
      initialize() {
        this.setupEventListeners();
        this.updateStatus('Ready to start', 'disconnected');
        this.updateConnectionInfo();
        this.log('info', 'Enhanced WebRTC Test Client initialized');

        // Start uptime counter
        this.state.startTime = Date.now();
        this.startUptimeCounter();
      }

      // Enhanced logging system
      log(level, message, data = null) {
        const timestamp = new Date();
        const logEntry = {
          timestamp,
          level,
          message,
          data
        };

        this.logs.push(logEntry);

        // Limit log history
        if (this.logs.length > this.maxLogs) {
          this.logs.shift();
        }

        // Display in UI
        this.displayLogEntry(logEntry);

        // Console logging
        const consoleMessage = `[${level.toUpperCase()}] ${message}`;
        switch (level) {
          case 'error':
            console.error(consoleMessage, data);
            this.state.errorCount++;
            this.updateErrorCount();
            break;
          case 'warning':
            console.warn(consoleMessage, data);
            break;
          case 'info':
            console.info(consoleMessage, data);
            break;
          case 'debug':
            console.debug(consoleMessage, data);
            break;
          default:
            console.log(consoleMessage, data);
        }
      }

      // Display log entry in UI
      displayLogEntry(logEntry) {
        const logElement = document.createElement('div');
        logElement.className = 'log-entry';

        const timestamp = logEntry.timestamp.toLocaleTimeString();
        const timeElement = document.createElement('span');
        timeElement.className = 'log-timestamp';
        timeElement.textContent = timestamp;

        const levelElement = document.createElement('span');
        levelElement.className = `log-level ${logEntry.level}`;
        levelElement.textContent = logEntry.level;

        const messageElement = document.createElement('span');
        messageElement.className = 'log-message';
        messageElement.textContent = logEntry.message;

        if (logEntry.data) {
          messageElement.textContent += ` | ${JSON.stringify(logEntry.data)}`;
        }

        logElement.appendChild(timeElement);
        logElement.appendChild(levelElement);
        logElement.appendChild(messageElement);

        this.elements.logsDiv.appendChild(logElement);
        this.elements.logsDiv.scrollTop = this.elements.logsDiv.scrollHeight;
      }

      // Clear logs
      clearLogs() {
        this.logs = [];
        this.elements.logsDiv.innerHTML = '';
        this.log('info', 'Logs cleared');
      }

      // Export logs
      exportLogs() {
        const logsText = this.logs.map(log =>
          `${log.timestamp.toISOString()} [${log.level.toUpperCase()}] ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
        ).join('\n');

        const blob = new Blob([logsText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `webrtc-test-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.log('info', 'Logs exported successfully');
      }

      // Update status display
      updateStatus(message, type) {
        const statusSpan = this.elements.statusDiv.querySelector('span');
        if (statusSpan) {
          statusSpan.textContent = `Status: ${message}`;
        }
        this.elements.statusDiv.className = `status ${type}`;
        this.log('info', `Status updated: ${message} (${type})`);
      }

      // Setup event listeners
      setupEventListeners() {
        // Connection controls
        this.elements.startButton.addEventListener('click', () => this.startTest());
        this.elements.stopButton.addEventListener('click', () => this.stopTest());
        this.elements.restartButton.addEventListener('click', () => this.restartTest());

        // Media controls
        this.elements.toggleVideoButton.addEventListener('click', () => this.toggleVideo());
        this.elements.toggleAudioButton.addEventListener('click', () => this.toggleAudio());
        this.elements.switchCameraButton.addEventListener('click', () => this.switchCamera());

        // Test actions
        this.elements.pingButton.addEventListener('click', () => this.sendPing());
        this.elements.statsButton.addEventListener('click', () => this.getStats());
        this.elements.clearLogsButton.addEventListener('click', () => this.clearLogs());
        this.elements.clearLogsButton2.addEventListener('click', () => this.clearLogs());
        this.elements.exportLogsButton.addEventListener('click', () => this.exportLogs());

        // Configuration changes
        this.elements.sessionIdInput.addEventListener('change', (e) => {
          this.config.sessionId = e.target.value;
          this.log('info', `Session ID updated: ${this.config.sessionId}`);
        });

        this.elements.wsUrlInput.addEventListener('change', (e) => {
          this.config.wsUrl = e.target.value;
          this.log('info', `WebSocket URL updated: ${this.config.wsUrl}`);
        });

        // Video quality change
        this.elements.videoQualitySelect.addEventListener('change', (e) => {
          this.log('info', `Video quality changed: ${e.target.value}`);
          if (this.state.isActive) {
            this.log('warning', 'Video quality change requires restart');
          }
        });

        // Audio enable/disable
        this.elements.audioEnabledSelect.addEventListener('change', (e) => {
          this.log('info', `Audio setting changed: ${e.target.value}`);
          if (this.state.isActive) {
            this.log('warning', 'Audio setting change requires restart');
          }
        });
      }

      // Update connection info display
      updateConnectionInfo() {
        this.elements.connectionState.textContent = this.state.peerConnection?.connectionState || 'Disconnected';
        this.elements.iceState.textContent = this.state.peerConnection?.iceConnectionState || 'New';
        this.elements.signalingState.textContent = this.state.peerConnection?.signalingState || 'Stable';
        this.elements.currentSessionId.textContent = this.config.sessionId || '-';

        // Update media info
        const localTracks = this.state.localStream?.getTracks().length || 0;
        const remoteTracks = this.state.remoteStream?.getTracks().length || 0;
        this.elements.localTracks.textContent = localTracks;
        this.elements.remoteTracks.textContent = remoteTracks;

        // Update video info
        if (this.state.localStream) {
          const videoTrack = this.state.localStream.getVideoTracks()[0];
          if (videoTrack) {
            const settings = videoTrack.getSettings();
            this.elements.videoResolution.textContent = `${settings.width}x${settings.height}`;
            this.elements.frameRate.textContent = `${settings.frameRate}fps`;

            // Update video overlay
            this.elements.localVideoInfo.textContent =
              `Local Video (${settings.width}x${settings.height}@${settings.frameRate}fps)`;
          }
        }

        // Update performance info
        this.elements.reconnectCount.textContent = this.state.reconnectAttempts;
        this.elements.errorCount.textContent = this.state.errorCount;

        if (this.state.connectionStartTime) {
          const connectionTime = Date.now() - this.state.connectionStartTime;
          this.elements.connectionTime.textContent = `${(connectionTime / 1000).toFixed(1)}s`;
        }
      }

      // Update error count
      updateErrorCount() {
        this.elements.errorCount.textContent = this.state.errorCount;
      }

      // Start uptime counter
      startUptimeCounter() {
        const updateUptime = () => {
          if (this.state.startTime) {
            const uptime = Date.now() - this.state.startTime;
            const seconds = Math.floor(uptime / 1000) % 60;
            const minutes = Math.floor(uptime / 60000) % 60;
            const hours = Math.floor(uptime / 3600000);
            this.elements.uptime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          }
        };

        const interval = setInterval(updateUptime, 1000);
        this.intervals.add(interval);
        updateUptime();
      }

      // Enable/disable controls based on state
      updateControlStates() {
        const isActive = this.state.isActive;
        const hasConnection = this.state.peerConnection && this.state.peerConnection.connectionState === 'connected';

        this.elements.startButton.disabled = isActive;
        this.elements.stopButton.disabled = !isActive;
        this.elements.restartButton.disabled = !isActive;

        this.elements.toggleVideoButton.disabled = !isActive;
        this.elements.toggleAudioButton.disabled = !isActive;
        this.elements.switchCameraButton.disabled = !isActive;

        this.elements.pingButton.disabled = !hasConnection;
        this.elements.statsButton.disabled = !hasConnection;
      }

      // WebSocket connection methods
      async connectWebSocket() {
        return new Promise((resolve, reject) => {
          try {
            const wsUrl = `${this.config.wsUrl}${this.config.sessionId}`;
            this.log('info', `Connecting to WebSocket: ${wsUrl}`);

            this.state.ws = new WebSocket(wsUrl);

            this.state.ws.onopen = () => {
              this.log('success', 'WebSocket connected successfully');
              this.state.reconnectAttempts = 0;

              // Send ready message
              this.sendWebSocketMessage({
                type: "webrtc_ready",
                sessionId: this.config.sessionId
              });

              resolve();
            };

            this.state.ws.onmessage = (event) => this.handleWebSocketMessage(event);
            this.state.ws.onclose = (event) => this.handleWebSocketClose(event);
            this.state.ws.onerror = (event) => {
              this.log('error', 'WebSocket connection error', {
                code: event.code,
                reason: event.reason
              });
              reject(new Error('WebSocket connection failed'));
            };

          } catch (error) {
            this.log('error', 'Failed to create WebSocket connection', error);
            reject(error);
          }
        });
      }

      // Handle WebSocket messages
      handleWebSocketMessage(event) {
        try {
          const message = JSON.parse(event.data);
          this.log('debug', `Received WebSocket message: ${message.type}`, message);

          switch (message.type) {
            case 'ping':
              this.handlePing(message);
              break;

            case 'webrtc_answer':
              this.handleAnswer(message.answer);
              break;

            case 'webrtc_candidate':
              this.handleCandidate(message.candidate);
              break;

            case 'error':
              this.handleServerError(message);
              break;

            case 'webrtc_error':
              this.handleWebRTCError(message);
              break;

            default:
              this.log('warning', `Unknown message type: ${message.type}`, message);
          }
        } catch (error) {
          this.log('error', 'Failed to parse WebSocket message', {
            error: error.message,
            data: event.data
          });
        }
      }

      // Handle WebSocket close
      handleWebSocketClose(event) {
        this.log('warning', `WebSocket connection closed`, {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        });

        if (this.state.isActive && this.state.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.attemptReconnect();
        } else if (this.state.reconnectAttempts >= this.config.maxReconnectAttempts) {
          this.log('error', 'Max reconnection attempts reached');
          this.updateStatus('Connection failed - max retries exceeded', 'error');
        }
      }

      // Attempt to reconnect WebSocket
      async attemptReconnect() {
        this.state.reconnectAttempts++;
        this.log('info', `Attempting to reconnect (${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
        this.updateStatus(`Reconnecting... (${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`, 'connecting');

        const timeout = setTimeout(async () => {
          try {
            await this.connectWebSocket();
            this.log('success', 'Reconnection successful');
            this.updateStatus('Reconnected', 'connected');
          } catch (error) {
            this.log('error', 'Reconnection failed', error);
            if (this.state.reconnectAttempts < this.config.maxReconnectAttempts) {
              this.attemptReconnect();
            }
          }
        }, this.config.reconnectDelay);

        this.timeouts.add(timeout);
      }

      // Send WebSocket message
      sendWebSocketMessage(message) {
        if (this.state.ws && this.state.ws.readyState === WebSocket.OPEN) {
          const messageStr = JSON.stringify(message);
          this.state.ws.send(messageStr);
          this.log('debug', `Sent WebSocket message: ${message.type}`, message);
          return true;
        } else {
          this.log('error', 'Cannot send message - WebSocket not connected', message);
          return false;
        }
      }

      // Handle ping message
      handlePing(message) {
        const pongMessage = {
          type: 'pong',
          timestamp: message.timestamp || Date.now()
        };
        this.sendWebSocketMessage(pongMessage);
        this.log('debug', 'Responded to ping with pong');
      }

      // Handle server error
      handleServerError(message) {
        this.log('error', `Server error: ${message.error}`, message);
        this.updateStatus(`Server error: ${message.error}`, 'error');
      }

      // Handle WebRTC error
      handleWebRTCError(message) {
        this.log('error', `WebRTC error: ${message.error}`, message);
        this.updateStatus(`WebRTC error: ${message.error}`, 'error');
      }

      // Media control methods
      async toggleVideo() {
        if (!this.state.localStream) return;

        const videoTrack = this.state.localStream.getVideoTracks()[0];
        if (videoTrack) {
          this.state.videoEnabled = !this.state.videoEnabled;
          videoTrack.enabled = this.state.videoEnabled;
          this.elements.toggleVideoButton.textContent = this.state.videoEnabled ? 'Disable Video' : 'Enable Video';
          this.log('info', `Video ${this.state.videoEnabled ? 'enabled' : 'disabled'}`);
        }
      }

      async toggleAudio() {
        if (!this.state.localStream) return;

        const audioTrack = this.state.localStream.getAudioTracks()[0];
        if (audioTrack) {
          this.state.audioEnabled = !this.state.audioEnabled;
          audioTrack.enabled = this.state.audioEnabled;
          this.elements.toggleAudioButton.textContent = this.state.audioEnabled ? 'Disable Audio' : 'Enable Audio';
          this.log('info', `Audio ${this.state.audioEnabled ? 'enabled' : 'disabled'}`);
        }
      }

      async switchCamera() {
        try {
          this.state.currentCamera = this.state.currentCamera === 'user' ? 'environment' : 'user';
          this.log('info', `Switching to ${this.state.currentCamera} camera`);

          // Stop current video track
          const videoTrack = this.state.localStream?.getVideoTracks()[0];
          if (videoTrack) {
            videoTrack.stop();
          }

          // Get new stream with different camera
          const newStream = await this.getUserMedia();

          // Replace video track in peer connection
          if (this.state.peerConnection) {
            const sender = this.state.peerConnection.getSenders().find(s =>
              s.track && s.track.kind === 'video'
            );
            if (sender) {
              await sender.replaceTrack(newStream.getVideoTracks()[0]);
            }
          }

          // Update local stream and video element
          this.state.localStream = newStream;
          this.elements.localVideo.srcObject = newStream;

          this.log('success', 'Camera switched successfully');

        } catch (error) {
          this.log('error', 'Failed to switch camera', error);
        }
      }

      // Test action methods
      sendPing() {
        const pingMessage = {
          type: 'ping',
          timestamp: Date.now()
        };

        if (this.sendWebSocketMessage(pingMessage)) {
          this.log('info', 'Ping sent to server');
        }
      }

      async getStats() {
        if (!this.state.peerConnection) {
          this.log('warning', 'No peer connection available for stats');
          return;
        }

        try {
          const stats = await this.state.peerConnection.getStats();
          const statsData = {};

          stats.forEach((report) => {
            if (report.type === 'inbound-rtp' || report.type === 'outbound-rtp') {
              statsData[report.type] = {
                bytesReceived: report.bytesReceived,
                bytesSent: report.bytesSent,
                packetsReceived: report.packetsReceived,
                packetsSent: report.packetsSent,
                packetsLost: report.packetsLost
              };
            }
          });

          this.log('info', 'WebRTC stats collected', statsData);
          this.updateNetworkStats(statsData);

        } catch (error) {
          this.log('error', 'Failed to get WebRTC stats', error);
        }
      }

      // Stats collection methods
      startStatsCollection() {
        if (this.statsInterval) return;

        this.statsInterval = setInterval(async () => {
          await this.collectAndUpdateStats();
        }, this.config.statsInterval);

        this.intervals.add(this.statsInterval);
        this.log('info', 'Started automatic stats collection');
      }

      stopStatsCollection() {
        if (this.statsInterval) {
          clearInterval(this.statsInterval);
          this.intervals.delete(this.statsInterval);
          this.statsInterval = null;
          this.log('info', 'Stopped automatic stats collection');
        }
      }

      async collectAndUpdateStats() {
        if (!this.state.peerConnection) return;

        try {
          const stats = await this.state.peerConnection.getStats();
          let bytesSent = 0, bytesReceived = 0, packetsLost = 0, rtt = 0;

          stats.forEach((report) => {
            if (report.type === 'outbound-rtp') {
              bytesSent += report.bytesSent || 0;
            } else if (report.type === 'inbound-rtp') {
              bytesReceived += report.bytesReceived || 0;
              packetsLost += report.packetsLost || 0;
            } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
              rtt = report.currentRoundTripTime || 0;
            }
          });

          this.updateNetworkStats({
            bytesSent,
            bytesReceived,
            packetsLost,
            roundTripTime: rtt
          });

        } catch (error) {
          this.log('debug', 'Stats collection error', error);
        }
      }

      updateNetworkStats(stats) {
        if (stats.bytesSent !== undefined) {
          this.elements.bytesSent.textContent = this.formatBytes(stats.bytesSent);
        }
        if (stats.bytesReceived !== undefined) {
          this.elements.bytesReceived.textContent = this.formatBytes(stats.bytesReceived);
        }
        if (stats.packetsLost !== undefined) {
          this.elements.packetsLost.textContent = stats.packetsLost;
        }
        if (stats.roundTripTime !== undefined) {
          this.elements.roundTripTime.textContent = `${(stats.roundTripTime * 1000).toFixed(1)}ms`;
        }
      }

      // Utility methods
      formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      // Cleanup method
      cleanup() {
        this.log('info', 'Cleaning up resources');
        this.stopTest();
      }
    }

    // Initialize the enhanced WebRTC client
    const client = new EnhancedWebRTCClient();

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      client.cleanup();
    });
  </script>
</body>
</html>
      
      log(message) {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        this.elements.logsDiv.appendChild(logEntry);
        this.elements.logsDiv.scrollTop = this.elements.logsDiv.scrollHeight;
        console.log(message);
      }
      
      updateStatus(message, type) {
        this.elements.statusDiv.textContent = `Status: ${message}`;
        this.elements.statusDiv.className = `status ${type}`;
      }
      
      updateConnectionInfo() {
        if (!this.state.peerConnection) return;
        
        const pc = this.state.peerConnection;
        const info = {
          signaling: pc.signalingState,
          ice: pc.iceConnectionState,
          connection: pc.connectionState
        };
        
        this.elements.connectionInfoDiv.textContent = JSON.stringify(info, null, 2);
      }
      
      setupEventListeners() {
        this.elements.startButton.addEventListener('click', () => this.start());
        this.elements.stopButton.addEventListener('click', () => this.stop());
        
        // Simplified cleanup on page unload
        window.addEventListener('beforeunload', () => {
          if (this.state.isActive) {
            this.cleanup();
          }
        });
      }
      
      async connectWebSocket() {
        return new Promise((resolve, reject) => {
          try {
            this.state.ws = new WebSocket(this.config.wsUrl);
            
            this.state.ws.onopen = () => {
              this.log("WebSocket connected");
              this.state.reconnectAttempts = 0;
              this.state.ws.send(JSON.stringify({ 
                type: "webrtc_ready", 
                sessionId: this.config.sessionId 
              }));
              resolve();
            };
            
            this.state.ws.onmessage = (event) => this.handleWebSocketMessage(event);
            this.state.ws.onclose = (e) => this.handleWebSocketClose(e);
            this.state.ws.onerror = (e) => {
              this.log(`WebSocket error: ${e.message || 'Unknown error'}`);
              reject(new Error('WebSocket connection failed'));
            };
            
          } catch (error) {
            reject(error);
          }
        });
      }
      
      handleWebSocketMessage(event) {
        try {
          const msg = JSON.parse(event.data);
          
          switch (msg.type) {
            case "ping":
              this.state.ws.send(JSON.stringify({ type: "pong", timestamp: msg.timestamp }));
              break;
              
            case "webrtc_answer":
              this.handleAnswer(msg.answer);
              break;
              
            case "webrtc_candidate":
              this.handleCandidate(msg.candidate);
              break;
              
            case "webrtc_error":
              this.log(`Server error: ${msg.error}`);
              this.updateStatus(`Error: ${msg.error}`, 'error');
              break;
          }
        } catch (error) {
          this.log(`Message parse error: ${error.message}`);
        }
      }
      
      handleWebSocketClose(e) {
        this.log(`WebSocket closed: ${e.code}`);
        
        if (this.state.isActive && this.state.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.state.reconnectAttempts++;
          this.log(`Reconnecting... (${this.state.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
          
          setTimeout(() => {
            if (this.state.isActive) {
              this.connectWebSocket().catch(() => {
                this.updateStatus('Connection failed', 'error');
              });
            }
          }, 2000);
        } else if (this.state.isActive) {
          this.updateStatus('Connection lost', 'error');
        }
      }
      
      async handleAnswer(answer) {
        if (!this.state.peerConnection || this.state.peerConnection.signalingState === 'closed') return;
        
        try {
          await this.state.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
          this.state.remoteDescSet = true;
          this.log("Remote description set");
          
          // Process queued candidates
          for (const candidate of this.state.candidateQueue) {
            await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
          }
          this.state.candidateQueue = [];
          
        } catch (error) {
          this.log(`Error setting remote description: ${error.message}`);
        }
      }
      
      async handleCandidate(candidate) {
        const rtcCandidate = {
          candidate: candidate.candidate,
          sdpMid: candidate.sdpMid,
          sdpMLineIndex: candidate.sdpMLineIndex
        };
        
        if (this.state.remoteDescSet && this.state.peerConnection) {
          try {
            await this.state.peerConnection.addIceCandidate(new RTCIceCandidate(rtcCandidate));
          } catch (error) {
            this.log(`Error adding ICE candidate: ${error.message}`);
          }
        } else {
          this.state.candidateQueue.push(rtcCandidate);
        }
      }
      
      setupPeerConnection() {
        this.state.peerConnection = new RTCPeerConnection({ iceServers: this.config.iceServers });
        const pc = this.state.peerConnection;
        
        // Add tracks
        this.state.localStream.getTracks().forEach(track => {
          pc.addTrack(track, this.state.localStream);
        });
        
        // Setup remote stream
        this.state.remoteStream = new MediaStream();
        this.elements.remoteVideo.srcObject = this.state.remoteStream;
        
        pc.ontrack = (event) => {
          event.streams[0].getTracks().forEach(track => {
            this.state.remoteStream.addTrack(track);
          });
        };
        
        pc.onicecandidate = (event) => {
          if (event.candidate && this.state.ws?.readyState === WebSocket.OPEN) {
            this.state.ws.send(JSON.stringify({
              type: "webrtc_candidate",
              candidate: {
                candidate: event.candidate.candidate,
                sdpMid: event.candidate.sdpMid,
                sdpMLineIndex: event.candidate.sdpMLineIndex
              },
              sessionId: this.config.sessionId
            }));
          }
        };
        
        pc.oniceconnectionstatechange = () => {
          this.updateConnectionInfo();
          
          if (pc.iceConnectionState === 'connected') {
            this.updateStatus('Connected', 'connected');
            this.log('WebRTC connected');
          } else if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
            this.updateStatus('Connection failed', 'error');
          }
        };
        
        // Update connection info periodically
        const infoInterval = setInterval(() => this.updateConnectionInfo(), 2000);
        this.intervals.add(infoInterval);
      }
      
      async start() {
        if (this.state.isActive) return;
        
        try {
          this.state.isActive = true;
          this.updateStatus('Starting...', 'disconnected');
          this.elements.startButton.disabled = true;
          
          // Get media first
          this.state.localStream = await navigator.mediaDevices.getUserMedia({ 
            video: true, 
            audio: true 
          });
          this.elements.localVideo.srcObject = this.state.localStream;
          this.log('Media acquired');
          
          // Connect WebSocket
          await this.connectWebSocket();
          
          // Setup peer connection
          this.setupPeerConnection();
          
          // Create and send offer
          const offer = await this.state.peerConnection.createOffer();
          await this.state.peerConnection.setLocalDescription(offer);
          
          this.state.ws.send(JSON.stringify({
            type: "webrtc_offer",
            offer,
            sessionId: this.config.sessionId
          }));
          
          this.updateStatus('Connecting...', 'disconnected');
          this.elements.stopButton.disabled = false;
          this.log('Connection initiated');
          
        } catch (error) {
          this.log(`Start error: ${error.message}`);
          this.updateStatus(`Error: ${error.message}`, 'error');
          this.stop();
        }
      }
      
      stop() {
        this.state.isActive = false;
        this.cleanup();
        this.updateStatus('Stopped', 'disconnected');
        this.elements.startButton.disabled = false;
        this.elements.stopButton.disabled = true;
        this.log('Stopped');
      }
      
      cleanup() {
        // Clear intervals
        this.intervals.forEach(interval => clearInterval(interval));
        this.intervals.clear();
        
        // Stop media tracks
        if (this.state.localStream) {
          this.state.localStream.getTracks().forEach(track => track.stop());
          this.state.localStream = null;
        }
        
        // Close peer connection
        if (this.state.peerConnection) {
          this.state.peerConnection.close();
          this.state.peerConnection = null;
        }
        
        // Close WebSocket
        if (this.state.ws && this.state.ws.readyState === WebSocket.OPEN) {
          this.state.ws.close(1000, 'Client cleanup');
          this.state.ws = null;
        }
        
        // Reset video elements
        this.elements.localVideo.srcObject = null;
        this.elements.remoteVideo.srcObject = null;
        
        // Reset state
        this.state.remoteStream = null;
        this.state.candidateQueue = [];
        this.state.remoteDescSet = false;
        this.state.reconnectAttempts = 0;
        
        this.elements.connectionInfoDiv.textContent = 'No connection';
      }
    }
    
    // Initialize the client
    const client = new WebRTCClient();
  </script>
</body>
</html>