# app/api/endpoints/websocket.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from app.services.interview_service import InterviewService
from app.services.websocket.manager import get_websocket_handler
from app.services.websocketv2.video_enhanced_handler import VideoEnhancedWebSocketHandler
from app.services.azure.azure_speech_service import AzureSpeechService
from app.services.websocket.webrtc_manager import WebRTCManager
from app.services.websocket.video_manager import VideoManager
from app.services.websocket.message_dispatcher import (
    message_dispatcher, message_validator, rate_limiter, MessageType
)
from app.services.websocket.config import config
from app.core.error_handling import (
    error_handler, websocket_error_handler, ErrorCode, ErrorContext, ErrorSeverity
)
from app.core.logging_config import log_performance
import json
import logging
import base64
import asyncio
import traceback
import uuid
from datetime import datetime, timezone

router = APIRouter()
logger = logging.getLogger(__name__)

# Global managers
webrtc_manager = WebRTCManager()
video_manager = VideoManager()

def get_interview_service() -> InterviewService:
    """Dependency injection cho InterviewService"""
    return InterviewService()

def get_video_websocket_handler() -> VideoEnhancedWebSocketHandler:
    """Get video-enhanced WebSocket handler"""
    return VideoEnhancedWebSocketHandler()

def get_azure_speech_service() -> AzureSpeechService:
    """Get Azure Speech Service"""
    return AzureSpeechService()


def _register_message_handlers(handler, interview_service, azure_speech):
    """Register message handlers with the dispatcher"""

    # Connection request handler
    async def handle_connection_request(websocket, session_id, message_data, context):
        await handler.utils.send_message(
            session_id,
            {
                "message": "Hẹ hẹ",
                "session_id": session_id,
                "status": "connected"
            },
            message_type="question_ready"
        )
        return True

    # TTS handler
    async def handle_tts_request(websocket, session_id, message_data, context):
        return await handle_tts_request_impl(websocket, message_data, azure_speech, session_id)

    # Pong handler
    async def handle_pong(websocket, session_id, message_data, context):
        logger.debug(f"Received pong from {session_id}")
        return True

    # WebRTC handlers
    async def handle_webrtc_offer(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_offer(message_data):
            await _send_error(websocket, "Invalid WebRTC offer")
            return False

        return await webrtc_manager.handle_offer(
            session_id, message_data.get("offer"), websocket
        )

    async def handle_webrtc_candidate(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_candidate(message_data):
            await _send_error(websocket, "Invalid WebRTC candidate")
            return False

        return await webrtc_manager.handle_ice_candidate(
            session_id, message_data.get("candidate")
        )

    async def handle_webrtc_ready(websocket, session_id, message_data, context):
        logger.info(f"WebRTC ready signal from {session_id}")
        await _send_ack(websocket, session_id, "webrtc_ready")
        return True

    # Video handlers
    async def handle_video_message(websocket, session_id, message_data, context):
        return await handler.handle_video_message(session_id, message_data)

    # Default candidate message handler
    async def handle_candidate_message(websocket, session_id, message_data, context):
        return await interview_service.communicate(session_id, message_data)

    # Register all handlers
    message_dispatcher.register_handler(
        MessageType.CONNECTION_REQUEST, handle_connection_request,
        "Handle connection request"
    )
    message_dispatcher.register_handler(
        MessageType.GENERATE_SPEECH, handle_tts_request,
        "Handle text-to-speech request"
    )
    message_dispatcher.register_handler(
        MessageType.PONG, handle_pong,
        "Handle pong message"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_OFFER, handle_webrtc_offer,
        "Handle WebRTC offer"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_CANDIDATE, handle_webrtc_candidate,
        "Handle WebRTC ICE candidate"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_READY, handle_webrtc_ready,
        "Handle WebRTC ready signal"
    )

    # Register video message handlers
    for msg_type in [MessageType.START_VIDEO_RECORDING, MessageType.VIDEO_CHUNK,
                     MessageType.END_VIDEO_RECORDING, MessageType.ANSWER_COMPLETED]:
        message_dispatcher.register_handler(
            msg_type, handle_video_message,
            f"Handle {msg_type.value}"
        )

    message_dispatcher.register_handler(
        MessageType.CANDIDATE_MESSAGE, handle_candidate_message,
        "Handle candidate message"
    )


async def _send_error(websocket: WebSocket, error_message: str, error_code: str = None, session_id: str = None):
    """Send enhanced error message to client"""
    try:
        # Create error context
        context = ErrorContext(
            session_id=session_id,
            operation="send_error",
            component="websocket_endpoint"
        )

        # Create error info
        error_info = error_handler.create_error(
            code=ErrorCode.WS_MESSAGE_INVALID if not error_code else ErrorCode(error_code),
            message=error_message,
            context=context,
            severity=ErrorSeverity.MEDIUM
        )

        # Send structured error response
        await error_handler.send_error_to_websocket(websocket, error_info)

    except Exception as e:
        logger.error(f"Failed to send error message: {e}")
        # Fallback to simple error message
        try:
            fallback_response = {
                "type": "error",
                "error": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send_text(json.dumps(fallback_response))
        except:
            pass  # Give up if even fallback fails


async def _send_ack(websocket: WebSocket, session_id: str, original_type: str):
    """Send acknowledgment message"""
    try:
        ack_response = {
            "type": "webrtc_ack",
            "sessionId": session_id,
            "originalType": original_type,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        await websocket.send_text(json.dumps(ack_response))
    except Exception as e:
        logger.error(f"Failed to send ack message: {e}")


async def _cleanup_session(session_id: str, handler, keep_alive_task: asyncio.Task):
    """Clean up session resources"""
    try:
        # Cancel keep-alive task
        if keep_alive_task and not keep_alive_task.done():
            keep_alive_task.cancel()
            try:
                await asyncio.wait_for(keep_alive_task, timeout=5.0)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass

        # Stop video processing
        await video_manager.stop_recording(session_id)

        # Close WebRTC connection
        await webrtc_manager.close_connection(session_id)

        # Disconnect WebSocket handler
        await handler.disconnect(session_id)

        # Clean up rate limiting
        rate_limiter.cleanup_session(session_id)

        logger.info(f"Session cleanup completed for {session_id}")

    except Exception as e:
        logger.error(f"Error during session cleanup for {session_id}: {e}")


@log_performance("video_track_handling", "video")
async def handle_video_track(track, session_id: str):
    """Handle incoming video track from WebRTC with comprehensive error handling"""
    context = ErrorContext(
        session_id=session_id,
        operation="video_track_handling",
        component="video_handler"
    )

    try:
        logger.info(f"Starting video processing for session {session_id}")

        # Start recording with error handling
        if not await video_manager.start_recording(session_id):
            error_info = error_handler.create_error(
                code=ErrorCode.VIDEO_RECORDING_FAILED,
                message=f"Failed to start video recording for session {session_id}",
                context=context,
                severity=ErrorSeverity.HIGH,
                recovery_suggestions=[
                    "Check video storage permissions",
                    "Verify disk space availability",
                    "Restart video recording"
                ]
            )
            logger.error(f"Failed to start video recording for {session_id}")
            return

        frame_count = 0
        consecutive_errors = 0
        max_consecutive_errors = 5

        # Process frames with enhanced error handling
        while webrtc_manager.is_processing_video(session_id):
            try:
                frame = await asyncio.wait_for(track.recv(), timeout=config.websocket.video_frame_timeout)
                if frame is None:
                    logger.info(f"Received None frame for session {session_id}, ending video processing")
                    break

                success = await video_manager.process_frame(session_id, frame)
                if success:
                    frame_count += 1
                    consecutive_errors = 0  # Reset error count on success
                else:
                    consecutive_errors += 1
                    logger.warning(f"Failed to process frame for session {session_id} (consecutive errors: {consecutive_errors})")

                    if consecutive_errors >= max_consecutive_errors:
                        error_info = error_handler.create_error(
                            code=ErrorCode.VIDEO_FRAME_INVALID,
                            message=f"Too many consecutive frame processing errors for session {session_id}",
                            context=context,
                            severity=ErrorSeverity.HIGH
                        )
                        logger.error(f"Stopping video processing due to consecutive errors for session {session_id}")
                        break

            except asyncio.TimeoutError:
                logger.debug(f"Timeout waiting for frame from session {session_id}")
                continue

            except Exception as e:
                consecutive_errors += 1

                # Determine error type and severity
                if "MediaStreamError" in str(e) or "connection" in str(e).lower():
                    logger.info(f"Media stream ended for session {session_id}")
                    break
                elif "codec" in str(e).lower() or "encoding" in str(e).lower():
                    error_info = error_handler.create_error(
                        code=ErrorCode.VIDEO_CODEC_ERROR,
                        message=f"Video codec error for session {session_id}: {str(e)}",
                        context=context,
                        exception=e,
                        severity=ErrorSeverity.MEDIUM
                    )
                else:
                    error_info = error_handler.create_error(
                        code=ErrorCode.VIDEO_FRAME_INVALID,
                        message=f"Video frame processing error for session {session_id}: {str(e)}",
                        context=context,
                        exception=e,
                        severity=ErrorSeverity.MEDIUM
                    )

                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"Stopping video processing due to consecutive errors for session {session_id}")
                    break
                continue

        # Stop recording and get final info
        recording_info = await video_manager.stop_recording(session_id)
        if recording_info:
            logger.info(f"Video processing completed for session {session_id}: frames={frame_count}, info={recording_info}")
        else:
            error_info = error_handler.create_error(
                code=ErrorCode.VIDEO_RECORDING_FAILED,
                message=f"Failed to stop video recording properly for session {session_id}",
                context=context,
                severity=ErrorSeverity.MEDIUM
            )

    except Exception as e:
        # Handle unexpected errors in video track processing
        error_info = error_handler.create_error(
            code=ErrorCode.VIDEO_RECORDING_FAILED,
            message=f"Unexpected video track handling error for session {session_id}: {str(e)}",
            context=context,
            exception=e,
            severity=ErrorSeverity.HIGH,
            recovery_suggestions=[
                "Restart video recording",
                "Check WebRTC connection",
                "Verify video codec support"
            ]
        )
        logger.error(f"Video track handling error for session {session_id}: {e}")

    finally:
        # Ensure cleanup always happens
        try:
            webrtc_manager.stop_video_processing(session_id)
            logger.debug(f"Video processing cleanup completed for session {session_id}")
        except Exception as cleanup_error:
            logger.error(f"Error during video processing cleanup for session {session_id}: {cleanup_error}")


@log_performance("tts_request", "audio")
async def handle_tts_request_impl(websocket: WebSocket, message: dict, azure_speech: AzureSpeechService, session_id: str = None) -> bool:
    """Handle text-to-speech requests with enhanced error handling"""
    try:
        # Validate TTS request
        if not message_validator.validate_tts_request(message):
            context = ErrorContext(
                session_id=session_id,
                operation="tts_validation",
                component="tts_handler"
            )
            error_info = error_handler.create_error(
                code=ErrorCode.VALIDATION_PARAMETER_INVALID,
                message="Invalid TTS request format",
                context=context
            )
            await error_handler.send_error_to_websocket(websocket, error_info)
            return False

        text = message.get("text", "")
        voice = message.get("voice", "vi-VN-HoaiMyNeural")

        if not text.strip():
            context = ErrorContext(
                session_id=session_id,
                operation="tts_validation",
                component="tts_handler"
            )
            error_info = error_handler.create_error(
                code=ErrorCode.VALIDATION_PARAMETER_MISSING,
                message="Text parameter is required for TTS",
                context=context
            )
            await error_handler.send_error_to_websocket(websocket, error_info)
            return False

        # Generate speech
        audio_data = await azure_speech.text_to_speech(text, voice)
        if not audio_data:
            context = ErrorContext(
                session_id=session_id,
                operation="tts_generation",
                component="azure_speech",
                additional_data={"text_length": len(text), "voice": voice}
            )
            error_info = error_handler.create_error(
                code=ErrorCode.AUDIO_TTS_FAILED,
                message="Azure Speech Service returned no audio data",
                context=context,
                severity=ErrorSeverity.HIGH,
                recovery_suggestions=[
                    "Check Azure Speech Service configuration",
                    "Verify network connectivity",
                    "Try with shorter text"
                ]
            )
            await error_handler.send_error_to_websocket(websocket, error_info)
            return False

        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        # Send response
        response = {
            "type": "tts_response",
            "text": text,
            "voice": voice,
            "audio": audio_base64,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        await websocket.send_text(json.dumps(response))

        # Log successful TTS generation
        logger.info(f"TTS generated successfully for session {session_id}", extra={
            "session_id": session_id,
            "text_length": len(text),
            "voice": voice,
            "audio_size": len(audio_data)
        })

        return True

    except Exception as e:
        context = ErrorContext(
            session_id=session_id,
            operation="tts_generation",
            component="tts_handler",
            additional_data={"exception_type": type(e).__name__}
        )

        # Determine specific error code based on exception
        if "timeout" in str(e).lower():
            code = ErrorCode.NETWORK_TIMEOUT
        elif "azure" in str(e).lower() or "speech" in str(e).lower():
            code = ErrorCode.EXTERNAL_AZURE_SPEECH_FAILED
        else:
            code = ErrorCode.AUDIO_TTS_FAILED

        error_info = error_handler.create_error(
            code=code,
            message=f"TTS generation failed: {str(e)}",
            context=context,
            exception=e,
            severity=ErrorSeverity.HIGH
        )

        await error_handler.send_error_to_websocket(websocket, error_info)
        return False


@router.websocket("/ws/interview/{session_id}")
async def websocket_interview_endpoint(
    websocket: WebSocket,
    session_id: str,
    interview_service: InterviewService = Depends(get_interview_service),
    azure_speech: AzureSpeechService = Depends(get_azure_speech_service)
):
    """Refactored WebSocket endpoint with improved architecture"""

    # Initialize handlers and managers
    handler = get_video_websocket_handler()

    # Register message handlers
    _register_message_handlers(handler, interview_service, azure_speech)

    # Establish connection
    connection_success = await handler.connect(websocket, session_id)
    if not connection_success:
        logger.error(f"Failed to establish connection for {session_id}")
        return

    # Start keep-alive task
    keep_alive_task = asyncio.create_task(keep_alive_loop(websocket, session_id))

    # Initialize WebRTC connection
    await webrtc_manager.create_connection(
        session_id,
        on_track_callback=handle_video_track
    )

    last_activity = datetime.now(timezone.utc)

    try:
        while True:
            try:
                # Receive message with timeout
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=config.websocket.connection_timeout
                )
                last_activity = datetime.now(timezone.utc)

                # Validate and parse message
                message = message_validator.validate_json(data)
                if not message:
                    await _send_error(websocket, "Invalid JSON format", ErrorCode.VALIDATION_MESSAGE_FORMAT.value, session_id)
                    continue

                if not message_validator.validate_message_structure(message):
                    await _send_error(websocket, "Invalid message structure", ErrorCode.VALIDATION_MESSAGE_FORMAT.value, session_id)
                    continue

                # Check rate limiting
                if not await rate_limiter.check_rate_limit(session_id):
                    context = ErrorContext(
                        session_id=session_id,
                        operation="rate_limiting",
                        component="websocket_endpoint"
                    )
                    error_info = error_handler.create_error(
                        code=ErrorCode.WS_RATE_LIMIT_EXCEEDED,
                        message=f"Rate limit exceeded for session {session_id}",
                        context=context,
                        severity=ErrorSeverity.MEDIUM
                    )
                    await error_handler.send_error_to_websocket(websocket, error_info)
                    continue

                # Dispatch message
                context = {
                    "handler": handler,
                    "interview_service": interview_service,
                    "azure_speech": azure_speech,
                    "last_activity": last_activity
                }

                success = await message_dispatcher.dispatch(
                    websocket, session_id, message, context
                )

                if not success:
                    logger.warning(f"Failed to process message for {session_id}")

            except asyncio.TimeoutError:
                # Check for activity timeout
                if (datetime.now(timezone.utc) - last_activity).total_seconds() > config.websocket.activity_timeout:
                    context = ErrorContext(
                        session_id=session_id,
                        operation="activity_timeout",
                        component="websocket_endpoint"
                    )
                    error_info = error_handler.create_error(
                        code=ErrorCode.WS_TIMEOUT,
                        message=f"Activity timeout for session {session_id}",
                        context=context,
                        severity=ErrorSeverity.MEDIUM
                    )
                    logger.warning(f"Connection timeout for {session_id}")
                    break
                continue

            except WebSocketDisconnect:
                logger.info(f"Client {session_id} disconnected normally")
                break

            except Exception as e:
                # Handle unexpected WebSocket errors
                context = ErrorContext(
                    session_id=session_id,
                    operation="websocket_receive",
                    component="websocket_endpoint",
                    additional_data={"exception_type": type(e).__name__}
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.WS_DISCONNECT_UNEXPECTED,
                    message=f"WebSocket receive error for {session_id}: {str(e)}",
                    context=context,
                    exception=e,
                    severity=ErrorSeverity.HIGH
                )
                logger.error(f"WebSocket receive error for {session_id}: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket error for {session_id}: {e}")
    finally:
        await _cleanup_session(session_id, handler, keep_alive_task)

async def keep_alive_loop(websocket: WebSocket, session_id: str):
    """Keep WebSocket connection alive with periodic pings"""
    try:
        while True:
            await asyncio.sleep(config.websocket.ping_interval)
            if websocket.client_state.value == 1:
                try:
                    await websocket.send_text(json.dumps({
                        "type": "ping",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }))
                    logger.debug(f"Sent ping to {session_id}")
                except Exception as e:
                    logger.warning(f"Failed to send ping to {session_id}: {e}")
                    break
            else:
                logger.info(f"WebSocket not connected for {session_id}, stopping keep-alive")
                break
    except asyncio.CancelledError:
        logger.debug(f"Keep-alive task cancelled for {session_id}")
    except Exception as e:
        logger.error(f"Keep-alive error for {session_id}: {e}")
