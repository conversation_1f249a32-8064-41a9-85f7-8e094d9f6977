# app/services/websocket/webrtc_manager.py
import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass, field

from aiortc import RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, RTCConfiguration, RTCIceServer
from fastapi import WebSocket

from .config import config

logger = logging.getLogger(__name__)


@dataclass
class WebRTCConnection:
    """Represents a WebRTC connection with metadata"""
    peer_connection: RTCPeerConnection
    session_id: str
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)
    state: str = "new"
    video_task: Optional[asyncio.Task] = None
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.utcnow()
    
    def is_expired(self, timeout: float = 300.0) -> bool:
        """Check if connection has expired"""
        return (datetime.utcnow() - self.last_activity).total_seconds() > timeout


class WebRTCManager:
    """Manages WebRTC peer connections and signaling"""

    def __init__(self):
        self.connections: Dict[str, WebRTCConnection] = {}
        self.video_processing_flags: Dict[str, bool] = {}
        self._cleanup_in_progress: Dict[str, bool] = {}  # Track cleanup to prevent double cleanup
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start periodic cleanup of expired connections"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def _periodic_cleanup(self):
        """Periodically clean up expired connections"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._cleanup_expired_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")
    
    async def _cleanup_expired_connections(self):
        """Clean up expired connections"""
        expired_sessions = []
        
        for session_id, connection in self.connections.items():
            if connection.is_expired():
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            logger.info(f"Cleaning up expired connection: {session_id}")
            await self.close_connection(session_id)
    
    async def create_connection(
        self, 
        session_id: str,
        on_track_callback: Optional[Callable] = None
    ) -> RTCPeerConnection:
        """Create a new WebRTC peer connection"""
        
        if session_id in self.connections:
            logger.warning(f"Connection already exists for session {session_id}")
            return self.connections[session_id].peer_connection
        
        try:
            # Create RTCConfiguration for aiortc
            ice_servers = []
            for server in config.webrtc.ice_servers:
                ice_servers.append(RTCIceServer(urls=server["urls"]))

            rtc_config = RTCConfiguration(iceServers=ice_servers)
            pc = RTCPeerConnection(configuration=rtc_config)
            
            connection = WebRTCConnection(
                peer_connection=pc,
                session_id=session_id
            )
            
            # Set up event handlers
            self._setup_connection_handlers(connection, on_track_callback)
            
            self.connections[session_id] = connection
            self.video_processing_flags[session_id] = True
            
            logger.info(f"Created WebRTC connection for session {session_id}")
            return pc
            
        except Exception as e:
            logger.error(f"Failed to create WebRTC connection for {session_id}: {e}")
            raise
    
    def _setup_connection_handlers(
        self, 
        connection: WebRTCConnection,
        on_track_callback: Optional[Callable] = None
    ):
        """Set up event handlers for the peer connection"""
        pc = connection.peer_connection
        session_id = connection.session_id
        
        @pc.on("track")
        def on_track(track):
            logger.info(f"Track {track.kind} received for session {session_id}")
            connection.update_activity()
            
            if track.kind == "video" and on_track_callback:
                task = asyncio.create_task(on_track_callback(track, session_id))
                connection.video_task = task
                task.add_done_callback(
                    lambda t: logger.info(f"Video task completed for session {session_id}")
                )
            elif track.kind == "audio":
                logger.info(f"Audio track received for session {session_id}")
        
        @pc.on("connectionstatechange")
        async def on_connection_state_change():
            connection.state = pc.connectionState
            connection.update_activity()
            logger.info(f"Connection state changed to {pc.connectionState} for {session_id}")
            
            if pc.connectionState in ["failed", "closed"]:
                await self.close_connection(session_id)
        
        @pc.on("iceconnectionstatechange")
        async def on_ice_connection_state_change():
            connection.update_activity()
            logger.info(f"ICE connection state: {pc.iceConnectionState} for {session_id}")
    
    async def handle_offer(
        self,
        session_id: str,
        offer_data: Dict[str, Any],
        websocket: WebSocket
    ) -> bool:
        """Handle WebRTC offer"""
        try:
            if session_id not in self.connections:
                logger.error(f"No connection found for session {session_id}")
                return False

            connection = self.connections[session_id]
            pc = connection.peer_connection

            # Debug logging
            logger.info(f"Received offer data for {session_id}: {offer_data}")

            # Validate offer data structure
            if not isinstance(offer_data, dict) or "sdp" not in offer_data or "type" not in offer_data:
                logger.error(f"Invalid offer data structure for {session_id}: {offer_data}")
                return False

            offer = RTCSessionDescription(
                sdp=offer_data["sdp"],
                type=offer_data["type"]
            )
            
            await pc.setRemoteDescription(offer)
            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
            
            # Send answer back
            response = {
                "type": "webrtc_answer",
                "answer": {
                    "sdp": pc.localDescription.sdp,
                    "type": pc.localDescription.type
                },
                "sessionId": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            await websocket.send_text(json.dumps(response))
            connection.update_activity()
            
            logger.info(f"Processed WebRTC offer for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling WebRTC offer for {session_id}: {e}")
            return False
    
    async def handle_ice_candidate(
        self,
        session_id: str,
        candidate_data: Dict[str, Any]
    ) -> bool:
        """Handle ICE candidate"""
        try:
            if session_id not in self.connections:
                logger.error(f"No connection found for session {session_id}")
                return False

            connection = self.connections[session_id]
            pc = connection.peer_connection

            # Debug logging
            logger.info(f"Received candidate data for {session_id}: {candidate_data}")

            # Validate candidate data
            if not candidate_data or not isinstance(candidate_data, dict):
                logger.warning(f"Invalid candidate data structure for {session_id}: {candidate_data}")
                return False

            if not candidate_data.get("candidate") or candidate_data.get("sdpMid") is None:
                logger.warning(f"Missing required candidate fields for {session_id}: {candidate_data}")
                return False
            
            # Parse candidate
            candidate = self._parse_ice_candidate(candidate_data)
            if candidate:
                await pc.addIceCandidate(candidate)
                connection.update_activity()
                logger.info(f"Added ICE candidate for session {session_id}")
                return True
            else:
                logger.warning(f"Failed to parse ICE candidate for {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling ICE candidate for {session_id}: {e}")
            return False
    
    def _parse_ice_candidate(self, candidate_data: Dict[str, Any]) -> Optional[RTCIceCandidate]:
        """Parse ICE candidate from data"""
        try:
            candidate_str = candidate_data.get("candidate", "")
            sdp_mid = candidate_data.get("sdpMid")
            sdp_mline_index = candidate_data.get("sdpMLineIndex", 0)

            # Debug logging
            logger.debug(f"Parsing candidate: {candidate_str}, sdpMid: {sdp_mid}, sdpMLineIndex: {sdp_mline_index}")

            # Remove "candidate:" prefix if present
            if candidate_str.startswith("candidate:"):
                candidate_str = candidate_str[10:]

            parts = candidate_str.split()
            if len(parts) >= 8:
                # Parse the candidate string according to RFC 5245
                # Format: foundation component protocol priority ip port typ type [raddr related-address] [rport related-port] [generation generation-value] [ufrag ice-ufrag] [network-id network-id]
                foundation = parts[0]
                component = int(parts[1])
                protocol = parts[2].lower()
                priority = int(parts[3])
                ip = parts[4]
                port = int(parts[5])

                # Find the 'typ' keyword and get the type
                typ_index = -1
                for i, part in enumerate(parts):
                    if part == "typ":
                        typ_index = i
                        break

                if typ_index == -1 or typ_index + 1 >= len(parts):
                    logger.warning(f"Invalid candidate format - missing 'typ': {candidate_str}")
                    return None

                candidate_type = parts[typ_index + 1]

                # Parse optional related address and port for relay/srflx candidates
                related_address = None
                related_port = None

                for i in range(len(parts) - 1):
                    if parts[i] == "raddr" and i + 1 < len(parts):
                        related_address = parts[i + 1]
                    elif parts[i] == "rport" and i + 1 < len(parts):
                        try:
                            related_port = int(parts[i + 1])
                        except ValueError:
                            pass

                return RTCIceCandidate(
                    component=component,
                    foundation=foundation,
                    ip=ip,
                    port=port,
                    priority=priority,
                    protocol=protocol,
                    type=candidate_type,
                    relatedAddress=related_address,
                    relatedPort=related_port,
                    sdpMid=sdp_mid,
                    sdpMLineIndex=int(sdp_mline_index) if sdp_mline_index is not None else 0
                )
            else:
                logger.warning(f"Invalid candidate format - insufficient parts: {candidate_str}")
                return None

        except (ValueError, IndexError, TypeError) as e:
            logger.error(f"Failed to parse ICE candidate: {e}, data: {candidate_data}")
            return None
    
    async def close_connection(self, session_id: str) -> bool:
        """Close and clean up a WebRTC connection"""
        try:
            # Check if cleanup is already in progress
            if self._cleanup_in_progress.get(session_id, False):
                logger.debug(f"Cleanup already in progress for {session_id}")
                return True

            if session_id not in self.connections:
                logger.debug(f"Connection {session_id} already closed or doesn't exist")
                return True

            # Mark cleanup as in progress
            self._cleanup_in_progress[session_id] = True

            connection = self.connections[session_id]

            # Cancel video task if running
            if connection.video_task and not connection.video_task.done():
                connection.video_task.cancel()
                try:
                    await asyncio.wait_for(connection.video_task, timeout=5.0)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    logger.debug(f"Video task cancelled for session {session_id}")
                    pass

            # Close peer connection safely
            try:
                await connection.peer_connection.close()
            except Exception as close_error:
                logger.warning(f"Error closing peer connection for {session_id}: {close_error}")

            # Clean up state safely
            try:
                del self.connections[session_id]
            except KeyError:
                logger.debug(f"Connection {session_id} already removed from connections")

            self.video_processing_flags.pop(session_id, None)

            logger.info(f"Closed WebRTC connection for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error closing connection for {session_id}: {type(e).__name__}: {e}")
            # Ensure cleanup even if there's an error
            try:
                self.connections.pop(session_id, None)
                self.video_processing_flags.pop(session_id, None)
            except Exception as cleanup_error:
                logger.error(f"Error during emergency cleanup for {session_id}: {cleanup_error}")
            return False
    
    def get_connection(self, session_id: str) -> Optional[WebRTCConnection]:
        """Get connection by session ID"""
        return self.connections.get(session_id)
    
    def is_processing_video(self, session_id: str) -> bool:
        """Check if session is processing video"""
        return self.video_processing_flags.get(session_id, False)
    
    def stop_video_processing(self, session_id: str):
        """Stop video processing for session"""
        self.video_processing_flags[session_id] = False
    
    async def cleanup_all(self):
        """Clean up all connections"""
        session_ids = list(self.connections.keys())
        for session_id in session_ids:
            await self.close_connection(session_id)
        
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get manager statistics"""
        return {
            "active_connections": len(self.connections),
            "video_processing_sessions": sum(1 for flag in self.video_processing_flags.values() if flag),
            "connections": {
                session_id: {
                    "state": conn.state,
                    "created_at": conn.created_at.isoformat(),
                    "last_activity": conn.last_activity.isoformat(),
                    "has_video_task": conn.video_task is not None and not conn.video_task.done()
                }
                for session_id, conn in self.connections.items()
            }
        }
